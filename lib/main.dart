import 'package:flutter/material.dart';
import 'package:flutter_connect_four_youtube/core/bindings/main_bindings.dart';
import 'package:get/get.dart';

import 'screens/game_screen/game_screen.dart';

void main() {
  runApp(C4());
}

class C4 extends StatelessWidget {
  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {
    return GetMaterialApp(
      initialBinding: MainBindings(),
      title: 'Flutter Demo',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        visualDensity: VisualDensity.adaptivePlatformDensity,
      ),
      initialRoute: '/',
      getPages: [
        GetPage(name: '/', page: () => GameScreen()),
      ],
    );
  }
}
